
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { MoreHorizontal, Download, Share2, Maximize2, RefreshCw } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const EmissionsChart = () => {
  const data = [
    { month: 'Jan', scope1: 85, scope2: 120, scope3: 25 },
    { month: 'Feb', scope1: 78, scope2: 115, scope3: 22 },
    { month: 'Mar', scope1: 82, scope2: 125, scope3: 28 },
    { month: 'Apr', scope1: 75, scope2: 110, scope3: 20 },
    { month: 'May', scope1: 80, scope2: 118, scope3: 24 },
    { month: 'Jun', scope1: 72, scope2: 105, scope3: 18 },
    { month: 'Jul', scope1: 68, scope2: 100, scope3: 15 },
    { month: 'Aug', scope1: 70, scope2: 102, scope3: 17 },
    { month: 'Sep', scope1: 65, scope2: 98, scope3: 19 },
    { month: 'Oct', scope1: 62, scope2: 95, scope3: 16 },
    { month: 'Nov', scope1: 60, scope2: 92, scope3: 14 },
    { month: 'Dec', scope1: 58, scope2: 90, scope3: 12 }
  ];

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Monthly GHG Emissions by Scope (tCO₂e)
        </h3>

        {/* Chart Context Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-500 hover:text-gray-700">
              <MoreHorizontal className="w-4 h-4" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem className="flex items-center space-x-2">
              <RefreshCw className="w-4 h-4" />
              <span>Refresh Data</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="flex items-center space-x-2">
              <Maximize2 className="w-4 h-4" />
              <span>View Full Screen</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export as PNG</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export as PDF</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export Data (CSV)</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="flex items-center space-x-2">
              <Share2 className="w-4 h-4" />
              <span>Share Chart</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip 
            contentStyle={{ 
              backgroundColor: 'white', 
              border: '1px solid #ccc',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend />
          <Bar dataKey="scope1" stackId="a" fill="#ef4444" name="Scope 1" />
          <Bar dataKey="scope2" stackId="a" fill="#f97316" name="Scope 2" />
          <Bar dataKey="scope3" stackId="a" fill="#22c55e" name="Scope 3" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default EmissionsChart;
