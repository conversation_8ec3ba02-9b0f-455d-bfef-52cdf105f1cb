
import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveC<PERSON><PERSON>, <PERSON> } from 'recharts';
import { MoreHorizontal, Download, Share2, Maximize2, RefreshCw, X } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

const EmissionsChart = () => {
  const data = [
    { month: 'Jan', scope1: 85, scope2: 120, scope3: 25 },
    { month: 'Feb', scope1: 78, scope2: 115, scope3: 22 },
    { month: 'Mar', scope1: 82, scope2: 125, scope3: 28 },
    { month: 'Apr', scope1: 75, scope2: 110, scope3: 20 },
    { month: 'May', scope1: 80, scope2: 118, scope3: 24 },
    { month: 'Jun', scope1: 72, scope2: 105, scope3: 18 },
    { month: 'Jul', scope1: 68, scope2: 100, scope3: 15 },
    { month: 'Aug', scope1: 70, scope2: 102, scope3: 17 },
    { month: 'Sep', scope1: 65, scope2: 98, scope3: 19 },
    { month: 'Oct', scope1: 62, scope2: 95, scope3: 16 },
    { month: 'Nov', scope1: 60, scope2: 92, scope3: 14 },
    { month: 'Dec', scope1: 58, scope2: 90, scope3: 12 }
  ];

  // Full screen modal state
  const [fullScreenChart, setFullScreenChart] = useState({
    isOpen: false,
    title: 'Monthly GHG Emissions by Scope (tCO₂e)',
  });

  // Chart ref for export functionality
  const chartRef = useRef<HTMLDivElement>(null);

  // Refresh data function
  const refreshChartData = () => {
    console.log('Refreshing emissions chart data');
    alert('Emissions chart data refreshed successfully!');
  };

  // Export chart as PNG
  const exportChartAsPNG = async () => {
    if (!chartRef.current) return;

    try {
      const canvas = await html2canvas(chartRef.current, {
        scale: 2,
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
      });

      const link = document.createElement('a');
      link.download = 'emissions_chart.png';
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Error exporting PNG:', error);
      alert('Failed to export chart as PNG');
    }
  };

  // Export chart as PDF
  const exportChartAsPDF = async () => {
    if (!chartRef.current) return;

    try {
      const canvas = await html2canvas(chartRef.current, {
        scale: 2,
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
      });

      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = 297; // A4 landscape width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      pdf.save('emissions_chart.pdf');
    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Failed to export chart as PDF');
    }
  };

  // Export chart data as CSV
  const exportChartDataAsCSV = () => {
    try {
      const headers = Object.keys(data[0] || {});
      const csvContent = [
        ['Monthly GHG Emissions by Scope (tCO₂e)'],
        [`Generated: ${new Date().toLocaleDateString()}`],
        [],
        headers,
        ...data.map(row => headers.map(header => row[header]))
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'emissions_data.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export chart data as CSV');
    }
  };

  // Share chart function
  const shareChart = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Monthly GHG Emissions by Scope (tCO₂e)',
          text: 'Check out this emissions chart',
          url: window.location.href
        });
      } catch (error) {
        console.error('Error sharing:', error);
        copyChartLink();
      }
    } else {
      copyChartLink();
    }
  };

  // Copy chart link to clipboard
  const copyChartLink = () => {
    navigator.clipboard.writeText(window.location.href).then(() => {
      alert('Link to emissions chart copied to clipboard!');
    }).catch(() => {
      alert('Failed to copy link to clipboard');
    });
  };

  // Open chart in full screen
  const openFullScreen = () => {
    setFullScreenChart({
      isOpen: true,
      title: 'Monthly GHG Emissions by Scope (tCO₂e)',
    });
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Monthly GHG Emissions by Scope (tCO₂e)
        </h3>

        {/* Chart Context Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-500 hover:text-gray-700">
              <MoreHorizontal className="w-4 h-4" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem
              className="flex items-center space-x-2 cursor-pointer"
              onClick={refreshChartData}
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh Data</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="flex items-center space-x-2 cursor-pointer"
              onClick={openFullScreen}
            >
              <Maximize2 className="w-4 h-4" />
              <span>View Full Screen</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="flex items-center space-x-2 cursor-pointer"
              onClick={exportChartAsPNG}
            >
              <Download className="w-4 h-4" />
              <span>Export as PNG</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="flex items-center space-x-2 cursor-pointer"
              onClick={exportChartAsPDF}
            >
              <Download className="w-4 h-4" />
              <span>Export as PDF</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="flex items-center space-x-2 cursor-pointer"
              onClick={exportChartDataAsCSV}
            >
              <Download className="w-4 h-4" />
              <span>Export Data (CSV)</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="flex items-center space-x-2 cursor-pointer"
              onClick={shareChart}
            >
              <Share2 className="w-4 h-4" />
              <span>Share Chart</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div ref={chartRef}>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #ccc',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Legend />
            <Bar dataKey="scope1" stackId="a" fill="#ef4444" name="Scope 1" />
            <Bar dataKey="scope2" stackId="a" fill="#f97316" name="Scope 2" />
            <Bar dataKey="scope3" stackId="a" fill="#22c55e" name="Scope 3" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Full Screen Chart Modal */}
      <Dialog open={fullScreenChart.isOpen} onOpenChange={(open) => setFullScreenChart(prev => ({ ...prev, isOpen: open }))}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0">
          <DialogHeader className="p-6 pb-0">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-xl font-semibold">
                {fullScreenChart.title}
              </DialogTitle>
              <div className="flex items-center space-x-2">
                {/* Full Screen Chart Actions */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshChartData}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem
                      className="flex items-center space-x-2 cursor-pointer"
                      onClick={exportChartAsPNG}
                    >
                      <Download className="w-4 h-4" />
                      <span>Export as PNG</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="flex items-center space-x-2 cursor-pointer"
                      onClick={exportChartAsPDF}
                    >
                      <Download className="w-4 h-4" />
                      <span>Export as PDF</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="flex items-center space-x-2 cursor-pointer"
                      onClick={exportChartDataAsCSV}
                    >
                      <Download className="w-4 h-4" />
                      <span>Export Data (CSV)</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="flex items-center space-x-2 cursor-pointer"
                      onClick={shareChart}
                    >
                      <Share2 className="w-4 h-4" />
                      <span>Share Chart</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </DialogHeader>
          <div className="flex-1 p-6 pt-4">
            <div className="w-full h-[70vh] bg-gradient-to-br from-gray-50 to-white rounded-lg p-6 border border-gray-100">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '1px solid #ccc',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                  <Legend />
                  <Bar dataKey="scope1" stackId="a" fill="#ef4444" name="Scope 1" />
                  <Bar dataKey="scope2" stackId="a" fill="#f97316" name="Scope 2" />
                  <Bar dataKey="scope3" stackId="a" fill="#22c55e" name="Scope 3" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EmissionsChart;
