import React from 'react';
import { RefreshCw, Download, Share2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChartUtils, ChartExportOptions } from '../utils/chartUtils';

interface FullScreenChartModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  chart: React.ReactNode;
  data: any[];
  onRefresh?: () => void;
  chartElement?: HTMLElement | null;
}

const FullScreenChartModal: React.FC<FullScreenChartModalProps> = ({
  isOpen,
  onOpenChange,
  title,
  chart,
  data,
  onRefresh,
  chartElement
}) => {
  const exportOptions: ChartExportOptions = {
    title,
    data,
    chartElement
  };

  const handleRefresh = () => {
    ChartUtils.refreshChartData(title, onRefresh);
  };

  const handleExportPNG = () => {
    ChartUtils.exportChartAsPNG(exportOptions);
  };

  const handleExportPDF = () => {
    ChartUtils.exportChartAsPDF(exportOptions);
  };

  const handleExportCSV = () => {
    ChartUtils.exportChartDataAsCSV(exportOptions);
  };

  const handleShare = () => {
    ChartUtils.shareChart(title);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0">
        <DialogHeader className="p-6 pb-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              {title}
            </DialogTitle>
            <div className="flex items-center space-x-2">
              {/* Full Screen Chart Actions */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Download className="w-4 h-4 mr-2" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem 
                    className="flex items-center space-x-2 cursor-pointer"
                    onClick={handleExportPNG}
                  >
                    <Download className="w-4 h-4" />
                    <span>Export as PNG</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    className="flex items-center space-x-2 cursor-pointer"
                    onClick={handleExportPDF}
                  >
                    <Download className="w-4 h-4" />
                    <span>Export as PDF</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    className="flex items-center space-x-2 cursor-pointer"
                    onClick={handleExportCSV}
                  >
                    <Download className="w-4 h-4" />
                    <span>Export Data (CSV)</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="flex items-center space-x-2 cursor-pointer"
                    onClick={handleShare}
                  >
                    <Share2 className="w-4 h-4" />
                    <span>Share Chart</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </DialogHeader>
        <div className="flex-1 p-6 pt-4">
          <div className="w-full h-[70vh] bg-gradient-to-br from-gray-50 to-white rounded-lg p-6 border border-gray-100">
            {chart}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FullScreenChartModal;
